'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import {
  logout as logoutClient,
  login as loginClient,
  register as registerClient,
  getAuthToken
} from '@/lib/clientAuth';

// --- NO CHANGES IN THIS SECTION ---
// Customer context type
interface CustomerContextType {
  customer: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  token: string | null;
  login: (credentials: {email: string, password: string}) => Promise<void>;
  register: (registration: {email: string, firstName: string, lastName: string, password: string}) => Promise<void>;
  logout: () => void;
  updateProfile: (data: any) => Promise<any>;
  error: string | null;
  refreshCustomer: () => Promise<void>;
}

// Create the context with default values
const CustomerContext = createContext<CustomerContextType>({
  customer: null,
  isLoading: true,
  isAuthenticated: false,
  token: null,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  updateProfile: async () => {},
  error: null,
  refreshCustomer: async () => {}
});

// Custom hook to use the customer context
export const useCustomer = () => useContext(CustomerContext);
// --- END OF NO CHANGES SECTION ---


// Define props to include addToast
interface CustomerProviderProps {
  children: ReactNode;
  addToast: (message: string, type: 'success' | 'error' | 'info') => void;
}

// Provider component with full authentication functionality
export const CustomerProvider = ({ children, addToast }: CustomerProviderProps) => { // <-- MODIFIED: Accept addToast as a prop
  const [customer, setCustomer] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const router = useRouter();
  // const { addToast } = useToast(); // <-- REMOVED: No longer importing useToast directly

// --- NO OTHER CHANGES ARE NEEDED IN THE REST OF THE FILE ---
// All the logic for login, register, logout, etc., remains the same.
// It will now use the `addToast` function passed in via props.

  // Transform customer data to ensure it has all required fields
  const transformCustomerData = (customerData: any) => {
    if (!customerData) return null;

    return {
      ...customerData,
      displayName: customerData.displayName || customerData.username || `${customerData.firstName || ''} ${customerData.lastName || ''}`.trim() || 'User'
    };
  };

  // Check authentication and get customer data from API
  const checkAuthAndGetCustomer = async () => {
    try {
      console.log('CustomerProvider: Checking authentication via /api/auth/me');
      const response = await fetch('/api/auth/me', {
        method: 'GET',
        credentials: 'include', // Include HTTP-only cookies
      });

      console.log('CustomerProvider: Auth API response status:', response.status);
      const result = await response.json();
      console.log('CustomerProvider: Auth API result:', result);

      if (result.success && result.customer) {
        return { success: true, customer: result.customer };
      } else {
        return { success: false, message: result.message || 'Not authenticated' };
      }
    } catch (error) {
      console.error('CustomerProvider: Error checking authentication:', error);
      return { success: false, message: 'Network error' };
    }
  };

  // Parse and handle authentication errors with more specific messages
  const parseAuthError = (error: any): string => {
    if (!error) return 'An unknown error occurred';

    const errorMessage = typeof error === 'string'
      ? error
      : error.message || JSON.stringify(error);

    // Common authentication errors
    if (errorMessage.includes('Unidentified customer')) {
      return 'The email or password you entered is incorrect. Please try again.';
    }

    if (errorMessage.includes('already associated')) {
      return 'An account with this email already exists. Please sign in instead.';
    }

    if (errorMessage.includes('password') && errorMessage.includes('too short')) {
      return 'Your password must be at least 8 characters. Please try again.';
    }

    if (errorMessage.includes('token') && (errorMessage.includes('expired') || errorMessage.includes('invalid'))) {
      return 'Your login session has expired. Please sign in again.';
    }

    if (errorMessage.includes('network') || errorMessage.includes('failed to fetch')) {
      return 'Network connection issue. Please check your internet connection and try again.';
    }

    // Return the original error if no specific handling
    return errorMessage;
  };
  // Enhanced login function with better error handling
  const login = async (credentials: {email: string, password: string}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await loginClient(credentials.email, credentials.password);

      if (!result || !result.success || !result.user) {
        throw new Error('Login failed: No user data returned');
      }

      // Convert user to customer format
      const customer = {
        id: result.user.id,
        databaseId: result.user.databaseId,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName
      };

      setCustomer(transformCustomerData(customer));
      setToken(getAuthToken()); // Get the token from cookies

      // Show success toast notification
      addToast(`Welcome back, ${customer?.firstName || 'there'}!`, 'success');

      // Redirect to homepage
      router.push('/');
    } catch (err) {
      const errorMessage = parseAuthError(err);
      setError(errorMessage);

      // Show error toast notification
      addToast(errorMessage, 'error');

      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced register function with better error handling
  const register = async (registration: {email: string, firstName: string, lastName: string, password: string}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await registerClient(
        registration.email,
        registration.firstName,
        registration.lastName,
        registration.password
      );

      if (!result || !result.success || !result.customer) {
        throw new Error('Registration failed: No customer data returned');
      }

      setCustomer(transformCustomerData(result.customer));
      setToken(getAuthToken()); // Get the token from cookies

      // Show success toast notification
      addToast(`Welcome to Ankkor, ${result.customer?.firstName}!`, 'success');

      // Redirect to homepage
      router.push('/');
    } catch (err) {
      const errorMessage = parseAuthError(err);
      setError(errorMessage);

      // Show error toast notification
      addToast(errorMessage, 'error');

      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    logoutClient();
    setCustomer(null);
    setToken(null);

    // Show info toast notification
    addToast('You have been signed out successfully', 'info');

    router.push('/');
    router.refresh(); // Refresh to update UI based on auth state
  };

  // Function to update customer profile
  const updateProfile = async (data: any) => {
    try {
      // TODO: Implement profile update functionality
      console.log('Update profile called with data:', data);
      addToast('Profile update functionality will be implemented', 'info');
      return {};
    } catch (err) {
      const errorMessage = parseAuthError(err);
      setError(errorMessage);
      addToast(errorMessage, 'error');
      throw err;
    }
  };

  // Refresh customer data
  const refreshCustomer = async () => {
    try {
      const result = await checkAuthAndGetCustomer();
      if (result.success) {
        setCustomer(transformCustomerData(result.customer));
        setToken(getAuthToken());
        console.log('Customer data refreshed successfully');
      } else {
        console.log('Failed to refresh customer data:', result.message);
        setCustomer(null);
        setToken(null);
      }
    } catch (err) {
      console.error('Error refreshing customer data:', err);
      setCustomer(null);
      setToken(null);
    }
  };

  // Check if the customer is logged in on mount
  useEffect(() => {
    const checkCustomerSession = async () => {
      try {
        setIsLoading(true);

        // Check authentication and get customer data from API
        const result = await checkAuthAndGetCustomer();
        if (result.success) {
          console.log('Found valid authentication, customer data loaded');
          setCustomer(transformCustomerData(result.customer));
          setToken(getAuthToken());
        } else {
          console.log('No valid authentication found:', result.message);
          setCustomer(null);
          setToken(null);
        }
      } catch (err) {
        console.error('Error checking customer session:', err);
        // On error, clear any potentially corrupted session data
        logoutClient();
        setCustomer(null);
        setToken(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkCustomerSession();
  }, []);

  const isAuthenticated = !!customer && !!token;

  const value: CustomerContextType = {
    customer,
    isLoading,
    isAuthenticated,
    token,
    login,
    register,
    logout,
    updateProfile,
    error,
    refreshCustomer
  };

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
};